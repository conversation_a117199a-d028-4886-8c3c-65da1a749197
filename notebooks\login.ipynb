import sys
import dotenv
sys.path.append("..")
dotenv.load_dotenv()

BASE_URL = "https://sim2.taifex.com.tw"

import requests

session = requests.Session()
session.headers.update({
    "Connection": "Keep-Alive",
    "Content-Type": "application/json; charset=utf-8",
})

import os
ACCOUNT=  str(os.getenv("ACCOUNT"))
PASSWORD = str(os.getenv("PASSWORD"))

import json

resp = session.post(
    f"{BASE_URL}/api2/User/UserLogin",
    data=json.dumps({
        "LoginType": "TTB",
        "TTBVer": "2025.05.02.0930",
        "UserMail": ACCOUNT,
        "UserCyph": PASSWORD,
    }, ensure_ascii=False),
)


resp.status_code


import sys
sys.path.append("..")

import dotenv
dotenv.load_dotenv()

import os
ACCOUNT = str(os.getenv("ACCOUNT"))
PASSWORD = str(os.getenv("PASSWORD"))


from src.taifex import Client

client = Client(ACCOUNT, PASSWORD)

await client.init_session()

await client.login()

await client.get_game_list()

await client.enter_game("*********")

async def example_handler(data):
    print("===========")
    print("EXAMPLE_HANDLER:")
    print(data)
    print("===========")

client.register_message_callback(example_handler)

await client.connect_websocket()

# await client.subscribe(
#     ["TXFE5.TW-A"],
#     [
#         # "BA",
#         "Tick",
#     ]
# )


"""
Taifex Virtual Trading Platform Client

This module provides an asynchronous client for interacting with the Taifex Virtual Trading Platform.
It supports login, game management, WebSocket connections, and real-time data subscription.
"""

import asyncio
import base64
import json
import logging
import random
import time
import uuid
import zlib
from collections.abc import Coroutine
from typing import Any, Callable

import aiohttp
import websockets

from .response import GameInfo

# Default logger setup - will be configured per client instance
logger = logging.getLogger(__name__)


class Client:
    """
    Asynchronous client for Taifex Virtual Trading Platform.

    This client provides functionality to:
    - Authenticate with the platform
    - Retrieve and enter trading games
    - Establish WebSocket connections for real-time data
    - Subscribe to market data feeds

    Attributes:
        base_url (str): The base URL of the Taifex platform
        account (str): User account for authentication
        password (str): User password for authentication
        ttb_version_str (str): TTB version string for compatibility

    Example:
        async with Client("account", "password") as client:
            await client.login()
            games = await client.get_game_list()
            await client.enter_game(games[0].game_id)
            await client.connect_websocket()
    """

    def __init__(
        self,
        account: str,
        password: str,
        base_url: str = "https://sim2.taifex.com.tw",
        ttb_version_str: str = "2025.05.02.0930",
        log_level: int = logging.INFO,
    ):
        """
        Initialize the Taifex client.

        Args:
            account (str): User account for authentication
            password (str): User password for authentication
            base_url (str, optional): Base URL of the platform. Defaults to "https://sim2.taifex.com.tw"
            ttb_version_str (str, optional): TTB version string. Defaults to "2025.05.02.0930"
            log_level (int, optional): Logging level. Defaults to logging.INFO.
                                     Use logging.DEBUG, logging.INFO, logging.WARNING, logging.ERROR, etc.

        Raises:
            ValueError: If any required parameter is empty
        """
        if not base_url or not account or not password:
            raise ValueError("base_url, account, password cannot be empty")

        self.base_url = base_url
        self.account = account
        self.password = password
        self.ttb_version_str = ttb_version_str

        # Configure logging for this client instance
        self.__setup_logging(log_level)

        # Private attributes for session management
        self.__http_session: aiohttp.ClientSession | None = None
        self.__cookies = {}
        self.__jwt_token = None
        self.__ws_connection = None
        self.__ws_listener_task = None
        self.__message_callbacks = []
        self.__ws_session_uuid = None

    def __setup_logging(self, log_level: int):
        """
        Configure logging for this client instance.

        Args:
            log_level (int): The logging level to set
        """
        # Configure the logger for this module
        logger.setLevel(log_level)

        # Only add handler if none exists to avoid duplicate logs
        if not logger.handlers:
            # Create console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(log_level)

            # Create formatter
            formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            console_handler.setFormatter(formatter)

            # Add handler to logger
            logger.addHandler(console_handler)
        else:
            # Update existing handlers' levels
            for handler in logger.handlers:
                handler.setLevel(log_level)

    def set_log_level(self, log_level: int):
        """
        Change the logging level for this client instance.

        Args:
            log_level (int): The new logging level (e.g., logging.DEBUG, logging.INFO, etc.)
        """
        self.__setup_logging(log_level)

    async def __aenter__(self):
        """Async context manager entry."""
        await self.init_session()
        return self

    async def __aexit__(self, exc_type, exc_value, traceback):
        """Async context manager exit."""
        await self.close()

    async def init_session(self):
        """
        Initialize HTTP session if not already created.

        Creates a new aiohttp.ClientSession for making HTTP requests.
        """
        if self.__http_session is None or self.__http_session.closed:
            self.__http_session = aiohttp.ClientSession()
            logger.info("aiohttp.ClientSession created")

    async def __ensure_session(self) -> aiohttp.ClientSession:
        """
        Ensure HTTP session is available and return it.

        Returns:
            aiohttp.ClientSession: The active HTTP session
        """
        if self.__http_session is None or self.__http_session.closed:
            await self.init_session()
            assert self.__http_session is not None
        return self.__http_session

    def __create_websocket_session_id_encoded(self):
        """
        Create WebSocket session ID with UUID and platform-specific suffix.

        Generates a UUID, appends the platform suffix "$apex@tw", and encodes
        the result in Base64 format as required by the platform.

        Returns:
            str: Base64 encoded session ID
        """
        self.__ws_session_uuid = str(uuid.uuid4())
        identifier_suffix = "$apex@tw"
        combined_id = f"{self.__ws_session_uuid}{identifier_suffix}"
        encoded_id = base64.b64encode(combined_id.encode("utf-8")).decode("utf-8")
        return encoded_id

    def __is_heartbeat_or_session_message(self, data: dict) -> bool:
        """
        Check if the message is a heartbeat or session maintenance message.

        Args:
            data (dict): The parsed message data

        Returns:
            bool: True if the message is a heartbeat or session message
        """
        # Check for direct heartbeat format: {'hb': 'timestamp'}
        if isinstance(data, dict) and "hb" in data:
            return True

        # Check for compressed heartbeat format: {'payload': {'hb': ...}}
        if "payload" in data and isinstance(data["payload"], dict) and "hb" in data["payload"]:
            return True

        # Check for session maintenance messages (SessionID + Hostname only)
        if isinstance(data, dict) and "SessionID" in data and "Hostname" in data and len(data) == 2:
            return True

        # Check for other known heartbeat patterns
        if isinstance(data, dict) and data.get("type") == "heartbeat":
            return True

        return False

    async def login(self):
        """
        Authenticate with the Taifex platform.

        Performs login using account credentials and obtains JWT token and cookies
        for subsequent API calls.

        Raises:
            json.JSONDecodeError: If response cannot be parsed as JSON
            Exception: If login request fails with non-200 status code
        """
        session = await self.__ensure_session()

        login_url = f"{self.base_url}/api2/User/UserLogin"
        login_payload = {
            "UserMail": self.account,
            "UserCyph": self.password,
            "LoginType": "TTB",
            "TTBVer": self.ttb_version_str,
        }
        headers = {
            "Content-Type": "application/json; charset=utf-8",
        }

        logger.info(f"Logging in to {login_url}")
        async with session.post(login_url, json=login_payload, headers=headers) as response:
            response_text = await response.text()
            logger.info(f"Login Status: {response.status}")

            if response.status == 200:
                try:
                    login_data = json.loads(response_text)
                    if login_data.get("Code") == "0000":
                        self.__jwt_token = login_data.get("Token")
                        logger.info("Login successful. JWT Token obtained.")
                        # Store cookies for subsequent requests
                        for key, value in response.cookies.items():
                            self.__cookies[key] = value
                        logger.info("Cookies obtained.")
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to decode JSON. Response text: {response_text[:200]}...")
                    logger.error(f"Failed to decode JSON: {e}")
                    raise e
            else:
                logger.error(
                    f"Login request failed. Http status: {response.status}. Response text: {response_text[:200]}...",
                )
                raise Exception(f"Login request failed, Http status code: {response.status}")

    async def get_game_list(self):
        """
        Retrieve list of available trading games.

        Fetches the list of trading games available on the platform.
        Automatically attempts login if JWT token is not available.

        Returns:
            list[GameInfo]: List of available games as GameInfo objects

        Raises:
            json.JSONDecodeError: If response cannot be parsed as JSON
        """
        session = await self.__ensure_session()

        if not self.__jwt_token:
            logger.warning("JWT Token not found, attempting to login...")
            await self.login()

        game_list_url = f"{self.base_url}/api2/Game/GameList"
        headers = {
            "Content-Type": "application/json; charset=utf-8",
            "Authorization": f"Bearer {self.__jwt_token}",
        }

        logger.info(f"Getting game list from {game_list_url}")
        async with session.post(game_list_url, json={}, headers=headers) as response:
            response_text = await response.text()
            logger.info(f"Game List Status: {response.status}")

            if response.status == 200:
                try:
                    game_data = json.loads(response_text)
                    if game_data.get("Code") == "0000":
                        logger.info("Game list obtained successfully")
                        return [GameInfo.from_dict(game) for game in game_data.get("Data", {}).get("Games", [])]
                    else:
                        logger.error(f"Get game list failed: {game_data.get('Message')}")
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to decode JSON. Response text: {response_text[:200]}...")
                    logger.error(f"Failed to decode JSON: {e}")
                    raise e
            else:
                logger.error(
                    f"Get game list failed. Http status: {response.status}. Response text: {response_text[:200]}...",
                )

    async def enter_game(self, game_id: str):
        """
        Enter a specific trading game.

        Joins the specified trading game using the game ID.
        Automatically attempts login if JWT token is not available.

        Args:
            game_id (str): The ID of the game to enter

        Raises:
            json.JSONDecodeError: If response cannot be parsed as JSON
            Exception: If enter game request fails with non-200 status code
        """
        session = await self.__ensure_session()

        if not self.__jwt_token:
            logger.warning("JWT Token not found, attempting to login...")
            await self.login()

        game_enter_url = f"{self.base_url}/api2/Game/GameEnter"
        headers = {
            "Content-Type": "application/json; charset=utf-8",
            "Authorization": f"Bearer {self.__jwt_token}",
        }
        payload = {
            "GameID": game_id,
            "LoginType": "TTB",
        }

        logger.info(f"Entering game {game_id}")
        async with session.post(game_enter_url, json=payload, headers=headers) as response:
            response_text = await response.text()
            logger.info(f"Game Enter Status: {response.status}")

            if response.status == 200:
                try:
                    enter_data = json.loads(response_text)
                    if enter_data.get("Code") == "0000":
                        logger.info(f"Successfully entered game: {game_id}")
                    else:
                        logger.error(f"Enter game failed: {enter_data.get('Message')}")
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to decode JSON. Response text: {response_text[:200]}...")
                    logger.error(f"Failed to decode JSON: {e}")
                    raise e
            else:
                logger.error(
                    f"Enter game failed. Http status: {response.status}. Response text: {response_text[:200]}...",
                )
                raise Exception(f"Enter game failed, Http status code: {response.status}")

    async def connect_websocket(self):
        """
        Establish WebSocket connection and start message listening.

        Creates a WebSocket connection to the platform for real-time data streaming.
        Uses SockJS protocol with platform-specific session management.

        Raises:
            Exception: If WebSocket connection fails
        """
        if self.__ws_connection is not None:
            logger.info("WebSocket connection already established. Reusing the existing connection.")
            return

        await self.__get_sockjs_info()

        ws_session_id_encoded = self.__create_websocket_session_id_encoded()
        server_id = str(random.randint(100, 999))
        websocket_uri = f"wss://{self.base_url.split('//')[1]}/wa/ws/{server_id}/{ws_session_id_encoded}/websocket"

        cookie_str = "; ".join([f"{name}={value}" for name, value in self.__cookies.items()])
        ws_headers = {
            "Cookie": cookie_str,
            "Origin": self.base_url,
        }
        logger.info(f"Connecting to WebSocket: {websocket_uri}...")

        try:
            self.__ws_connection = await websockets.connect(
                websocket_uri,
                additional_headers=ws_headers,
                user_agent_header=None,  # Prevent automatic user-agent header addition
                origin=None,  # Prevent automatic origin header addition
            )
            logger.info("WebSocket Connection Established.")
            # Start background task for message listening
            self.__ws_listener_task = asyncio.create_task(self.__listen_websocket())

        except websockets.exceptions.WebSocketException as e:
            logger.error(f"WebSocket Connection Failed: {e}")
            self.__ws_connection = None
            raise Exception(f"Connect WebSocket Failed: {e}") from e

    def register_message_callback(self, callback: Callable[[dict[str, Any]], Coroutine[Any, Any, None]]):
        """
        Register a callback function for WebSocket message handling.

        The callback will be invoked for each received WebSocket message.
        Callback should be an async function that accepts a dictionary parameter.

        Args:
            callback: Async function to handle received messages
                     Signature: async def callback(message: dict[str, Any]) -> None
        """
        if not asyncio.iscoroutinefunction(callback):
            logger.warning(f"Callback function {callback.__name__} is not a coroutine function. It may cause blocking.")
        logger.info(f"Registered callback function {callback.__name__}")
        self.__message_callbacks.append(callback)

    def unregister_message_callback(self, callback: Callable[[dict[str, Any]], Coroutine[Any, Any, None]]):
        """
        Remove a previously registered callback function.

        Args:
            callback: The callback function to remove
        """
        try:
            self.__message_callbacks.remove(callback)
            logger.info(f"Unregistered callback function {callback.__name__}")
        except ValueError:
            logger.warning(f"Callback function {callback.__name__} is not registered")

    async def subscribe(self, products: list, types: list) -> bool:
        """
        Subscribe to market data feeds for specified products and data types.

        Establishes subscription for real-time market data. Automatically connects
        WebSocket if not already connected.

        Args:
            products (list): List of product codes to subscribe to
            types (list): List of data types to subscribe to (e.g., price, volume)

        Returns:
            bool: True if subscription successful, False otherwise

        Raises:
            Exception: If WebSocket connection fails or session UUID is missing
        """
        session = await self.__ensure_session()

        if not self.__ws_connection:
            logger.warning("WebSocket not connected, attempting to connect...")
            await self.connect_websocket()
            if not self.__ws_connection:
                raise Exception("WebSocket connection failed, cannot subscribe.")

        if not self.__ws_session_uuid:
            logger.error("WebSocket session UUID not found, cannot subscribe.")
            raise Exception("WebSocket session UUID lost")

        reg_url = f"{self.base_url}/wa/api/reg"
        reg_payload_dict = {
            "SessionID": self.__ws_session_uuid,
            "Prods": products,
            "Types": types,
        }
        reg_payload_str = json.dumps(reg_payload_dict, ensure_ascii=False)
        cookie_str = "; ".join([f"{name}={value}" for name, value in self.__cookies.items()])
        reg_headers = {
            "Content-Type": "application/x-www-form-urlencoded",  # Platform-specific requirement
            "Cookie": cookie_str,
        }

        logger.info(f"Subscribing to {reg_url}...")
        logger.info(f"Subscription Payload (as string): {reg_payload_str}")

        async with session.post(reg_url, data=reg_payload_str.encode("utf-8"), headers=reg_headers) as response:
            response_text = await response.text()
            logger.info(f"Subscription Status: {response.status}")
            logger.info(f"Subscription Response: {response_text}")
            if response.status == 200 and '"result":"success"' in response_text.lower():
                logger.info("Subscription successful.")
                return True
            else:
                logger.error(f"Subscription {products} - {types} failed: {response_text}")
                return False

    async def close(self):
        """
        Clean up and close all connections.

        Properly closes WebSocket connection, cancels background tasks,
        and closes HTTP session to free resources.
        """
        logger.info("Preparing to close client connections...")

        # Cancel WebSocket listener task
        if self.__ws_listener_task:
            self.__ws_listener_task.cancel()
            try:
                await self.__ws_listener_task
            except asyncio.CancelledError:
                logger.info("WebSocket listener task cancelled.")

        # Close WebSocket connection
        if self.__ws_connection:
            logger.info("Closing WebSocket connection...")
            await self.__ws_connection.close()
            self.__ws_connection = None
            logger.info("WebSocket connection closed.")

        # Close HTTP session
        if self.__http_session and not self.__http_session.closed:
            logger.info("Closing aiohttp.ClientSession...")
            await self.__http_session.close()
            logger.info("aiohttp.ClientSession closed.")

        logger.info("Client closed successfully.")

    async def __get_sockjs_info(self):
        """
        Retrieve SockJS connection information.

        Fetches SockJS metadata required for WebSocket connection establishment.
        Updates cookies if provided in the response.

        Returns:
            dict: SockJS connection information

        Raises:
            json.JSONDecodeError: If response cannot be parsed as JSON
            Exception: If request fails with non-200 status code
        """
        session = await self.__ensure_session()

        cookie_str = "; ".join([f"{name}={value}" for name, value in self.__cookies.items()])
        timestamp = int(time.time() * 1000)
        info_url = f"{self.base_url}/wa/ws/info?t={timestamp}"
        info_headers = {"Cookie": cookie_str}

        logger.info(f"Fetching SockJS info from {info_url}...")

        async with session.get(info_url, headers=info_headers) as response:
            response_text = await response.text()
            logger.debug(f"SockJS Info Response: {response_text}")
            if response.status == 200:
                try:
                    info_data = json.loads(response_text)
                    logger.info(f"SockJS Info retrieved successfully: {info_data}")
                    # Some APIs may update cookies here
                    for key, value in response.cookies.items():
                        self.__cookies[key] = value
                    return info_data
                except json.JSONDecodeError as e:
                    logger.error(f"SockJS Info is not valid JSON: {response_text}")
                    raise e
            else:
                logger.error(f"Fetch SockJS info Failed, HTTP Status: {response.status}")
                raise Exception(f"Fetch SockJS info Failed, HTTP Status: {response.status}")

    async def __listen_websocket(self):
        """
        Listen to WebSocket messages and process them.

        Handles incoming WebSocket messages using SockJS protocol.
        Supports multiple message formats including:
        - SockJS control messages (open, heartbeat)
        - JSON data messages
        - Compressed data (zlib/base64 encoded)

        Invokes registered callbacks for each processed message.
        """
        if not self.__ws_connection:
            logger.error("WebSocket connection is not established. Cannot listen to messages.")
            return

        logger.info("Start listening to WebSocket messages...")
        try:
            async for message in self.__ws_connection:
                logger.debug(f"Received WebSocket message: {message}")
                parsed_data = None

                # Handle SockJS control messages
                if message == "o":
                    logger.info("SockJS connection established.")
                    continue
                elif message == "h":
                    logger.debug("Heartbeat received.")
                    continue
                elif isinstance(message, str) and message.startswith("a["):
                    try:
                        # SockJS array frame format: 'a["json_string_payload"]'
                        payload_wrapper_str = message[1:]  # Remove leading 'a'
                        payload_list = json.loads(payload_wrapper_str)

                        if payload_list and isinstance(payload_list, list):
                            for item_str in payload_list:
                                try:
                                    # Try direct JSON parsing first
                                    actual_data = json.loads(item_str)

                                    # Check if this is a heartbeat or session message
                                    if self.__is_heartbeat_or_session_message(actual_data):
                                        logger.debug(f"Heartbeat/session message received: {actual_data}")
                                        continue

                                    parsed_data = {"type": "data", "payload": actual_data}
                                    logger.info(f"Successfully parsed SockJS array frame: {actual_data}")
                                except json.JSONDecodeError:
                                    logger.debug(
                                        "Failed to parse item as direct JSON, attempting zlib/base64 decompression. "
                                        + f"Item: {item_str[:60]}..."
                                    )
                                    try:
                                        # Try base64 -> zlib -> json decompression
                                        decoded_base64 = base64.b64decode(item_str)
                                        try:
                                            decompressed_data_bytes = zlib.decompress(decoded_base64)
                                        except zlib.error as zde:
                                            # If standard zlib fails, try raw deflate
                                            logger.debug(
                                                f"Standard zlib.decompress failed ({zde}), trying raw deflate."
                                            )
                                            decompress_obj = zlib.decompressobj(-zlib.MAX_WBITS)
                                            decompressed_data_bytes = decompress_obj.decompress(decoded_base64)
                                            decompressed_data_bytes += decompress_obj.flush()

                                        decompressed_json_str = decompressed_data_bytes.decode("utf-8")
                                        actual_data = json.loads(decompressed_json_str)

                                        logger.debug(f"Successfully parsed compressed data: {actual_data}")
                                        # Check if this is a heartbeat or session message
                                        if self.__is_heartbeat_or_session_message(actual_data):
                                            logger.debug(f"Heartbeat/session message received: {actual_data}")
                                            continue

                                    except Exception as e_zlib:
                                        # Decompression failed, treat as raw data
                                        logger.warning(
                                            f"Failed to parse item using zlib/base64. Item: {item_str[:60]}. "
                                            + f"Error: {e_zlib}"
                                        )
                                        parsed_data = {"type": "raw_sockjs_array_item", "payload": item_str}
                        else:
                            parsed_data = {"type": "unknown_sockjs_array", "payload": payload_list}

                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse SockJS array frame (outer layer): {message}")
                        parsed_data = {"type": "raw_message", "payload": message}
                else:
                    # Handle non-SockJS messages
                    logger.info(f"Received unknown type or normal WebSocket message: {message}")
                    parsed_data = {"type": "unknown", "payload": message}

                # Invoke all registered callbacks
                if parsed_data:
                    for callback in self.__message_callbacks:
                        asyncio.create_task(callback(parsed_data))

        except websockets.exceptions.ConnectionClosed as e:
            logger.warning(f"WebSocket connection closed, code: {e.code}, reason: {e.reason}")
        except Exception as e:
            logger.error(f"Error in WebSocket listener: {e}", exc_info=True)
        finally:
            logger.info("WebSocket listener stopped.")
            self.__ws_connection = None  # Mark connection as closed

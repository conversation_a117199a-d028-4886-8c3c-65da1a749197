import asyncio
import base64
import json
import logging
import random
import time
import uuid
import zlib
from collections.abc import Coroutine
from typing import Any, Callable

import aiohttp
import websockets

from .response import GameInfo

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Client:
    """
    Taifex Virtual Trading Platform's Client
    """

    def __init__(
        self,
        account: str,
        password: str,
        base_url: str = "https://sim2.taifex.com.tw",
        ttb_version_str: str = "2025.05.02.0930",
    ):
        if not base_url or not account or not password:
            raise ValueError("base_url, account, password cannot be empty")

        self.base_url = base_url
        self.account = account
        self.password = password
        self.ttb_version_str = ttb_version_str

        self.__http_session: aiohttp.ClientSession | None = None
        self.__cookies = {}
        self.__jwt_token = None
        self.__ws_connection = None
        self.__ws_listener_task = None
        self.__message_callbacks = []
        self.__ws_session_uuid = None

    async def __aenter__(self):
        await self.init_session()
        return self

    async def __aexit__(self, exc_type, exc_value, traceback):
        await self.close()

    async def init_session(self):
        if self.__http_session is None or self.__http_session.closed:
            self.__http_session = aiohttp.ClientSession()
            logger.info("aiohttp.ClientSession created")

    async def __ensure_session(self) -> aiohttp.ClientSession:
        if self.__http_session is None or self.__http_session.closed:
            await self.init_session()
            assert self.__http_session is not None
        return self.__http_session

    def __create_websocket_session_id_encoded(self):
        """
        Create websocket session id (UUID + suffix($apex@tw) + Base64 encoded)
        """
        self.__ws_session_uuid = str(uuid.uuid4())  # native uuid
        identifier_suffix = "$apex@tw"
        combined_id = f"{self.__ws_session_uuid}{identifier_suffix}"
        encoded_id = base64.b64encode(combined_id.encode("utf-8")).decode("utf-8")
        return encoded_id

    async def login(self):
        """
        Login to taifex
        """
        session = await self.__ensure_session()

        login_url = f"{self.base_url}/api2/User/UserLogin"
        login_payload = {
            "UserMail": self.account,
            "UserCyph": self.password,
            "LoginType": "TTB",
            "TTBVer": self.ttb_version_str,
        }
        headers = {
            "Content-Type": "application/json; charset=utf-8",
        }

        logger.info(f"Logging in to {login_url}")
        async with session.post(login_url, json=login_payload, headers=headers) as response:
            response_text = await response.text()
            logger.info(f"Login Status: {response.status}")

            if response.status == 200:
                try:
                    login_data = json.loads(response_text)
                    if login_data.get("Code") == "0000":
                        self.__jwt_token = login_data.get("Token")
                        logger.info("Login successful. JWT Token obtained.")
                        for key, value in response.cookies.items():
                            self.__cookies[key] = value
                        logger.info("Cookies obtained.")
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to decode JSON. Response text: {response_text[:200]}...")
                    logger.error(f"Failed to decode JSON: {e}")
                    raise e
            else:
                logger.error(
                    f"Login request failed. Http status: {response.status}. Response text: {response_text[:200]}...",
                )
                raise Exception(f"Login request failed, Http status code: {response.status}")

    async def get_game_list(self):
        """
        Get game list
        """
        session = await self.__ensure_session()

        if not self.__jwt_token:
            logger.warning("JWT Token not found, try to login...")
            await self.login()

        game_list_url = f"{self.base_url}/api2/Game/GameList"
        headers = {
            "Content-Type": "application/json; charset=utf-8",
            "Authorization": f"Bearer {self.__jwt_token}",
        }

        logger.info(f"Getting game list from {game_list_url}")
        async with session.post(game_list_url, json={}, headers=headers) as response:
            response_text = await response.text()
            logger.info(f"Game List Status: {response.status}")

            if response.status == 200:
                try:
                    game_data = json.loads(response_text)
                    if game_data.get("Code") == "0000":
                        logger.info("Game list obtained successfully")
                        return [GameInfo.from_dict(game) for game in game_data.get("Data", {}).get("Games", [])]
                    else:
                        logger.error(f"Get game list failed: {game_data.get('Message')}")
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to decode JSON. Response text: {response_text[:200]}...")
                    logger.error(f"Failed to decode JSON: {e}")
                    raise e
            else:
                logger.error(
                    f"Get game list failed. Http status: {response.status}. Response text: {response_text[:200]}...",
                )

    async def enter_game(self, game_id: str):
        """
        Enter specific game
        """
        session = await self.__ensure_session()

        if not self.__jwt_token:
            logger.warning("JWT Token not found, try to login...")
            await self.login()

        game_enter_url = f"{self.base_url}/api2/Game/GameEnter"
        headers = {
            "Content-Type": "application/json; charset=utf-8",
            "Authorization": f"Bearer {self.__jwt_token}",
        }
        payload = {
            "GameID": game_id,
            "LoginType": "TTB",
        }

        logger.info(f"Entering game {game_id}")
        async with session.post(game_enter_url, json=payload, headers=headers) as response:
            response_text = await response.text()
            logger.info(f"Game Enter Status: {response.status}")

            if response.status == 200:
                try:
                    enter_data = json.loads(response_text)
                    if enter_data.get("Code") == "0000":
                        logger.info(f"Enter game: {game_id} successfully")
                        return True
                    else:
                        logger.error(f"Enter game failed: {enter_data.get('Message')}")
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to decode JSON. Response text: {response_text[:200]}...")
                    logger.error(f"Failed to decode JSON: {e}")
                    raise e
            else:
                logger.error(
                    f"Enter game failed. Http status: {response.status}. Response text: {response_text[:200]}...",
                )
                raise Exception(f"Enter game failed, Http status code: {response.status}")

    async def connect_websocket(self):
        """
        Connect to websocket. Start listening to messages.
        """
        if self.__ws_connection is not None:
            logger.info("WebSocket connection already established. Reusing the existing connection.")
            return

        await self.__get_sockjs_info()

        ws_session_id_encoded = self.__create_websocket_session_id_encoded()
        server_id = str(random.randint(100, 999))
        websocket_uri = f"wss://{self.base_url.split('//')[1]}/wa/ws/{server_id}/{ws_session_id_encoded}/websocket"

        cookie_str = "; ".join([f"{name}={value}" for name, value in self.__cookies.items()])
        ws_headers = {
            "Cookie": cookie_str,
            "Origin": self.base_url,
        }
        logger.info(f"Connecting to WebSocket: {websocket_uri}...")

        try:
            self.__ws_connection = await websockets.connect(
                websocket_uri,
                additional_headers=ws_headers,
                user_agent_header=None,  # Avoid websockets add user-agent header automatically
                origin=None,  # Avoid websockets add origin header automatically
            )
            logger.info("WebSocket Connection Established.")
            # Start listening to messages
            self.__ws_listener_task = asyncio.create_task(self.__listen_websocket())

        except websockets.exceptions.WebSocketException as e:
            logger.error(f"WebSocket Connection Failed: {e}")
            self.__ws_connection = None
            raise Exception(f"Connect WebSocket Failed: {e}") from e

    def register_message_callback(self, callback: Callable[[dict[str, Any]], Coroutine[Any, Any, None]]):
        """
        Register a callback function to be called when a message is received.
        """
        if not asyncio.iscoroutinefunction(callback):  # check if callback is a coroutine function
            logger.warning(f"Callback function {callback.__name__} is not a coroutine function. It may cause blocking.")
        logger.info(f"Registered callback function {callback.__name__}")
        self.__message_callbacks.append(callback)

    def unregister_message_callback(self, callback: Callable[[dict[str, Any]], Coroutine[Any, Any, None]]):
        """
        Unregister a callback function.
        """
        try:
            self.__message_callbacks.remove(callback)
            logger.info(f"Unregistered callback function {callback.__name__}")
        except ValueError:
            logger.warning(f"Callback function {callback.__name__} is not registered")

    async def subscribe(self, products: list, types: list) -> bool:
        """
        Subscribe.
        """
        session = await self.__ensure_session()

        if not self.__ws_connection:
            logger.warning("WebSocket not connected, try to connect...")
            await self.connect_websocket()
            if not self.__ws_connection:
                raise Exception("WebSocket connection failed, cannot subscribe.")

        if not self.__ws_session_uuid:
            logger.error("WebSocket session UUID not found, cannot subscribe.")
            raise Exception("WebSocket session UUID lose")

        reg_url = f"{self.base_url}/wa/api/reg"
        reg_payload_dict = {
            "SessionID": self.__ws_session_uuid,
            "Prods": products,
            "Types": types,
        }
        reg_payload_str = json.dumps(reg_payload_dict, ensure_ascii=False)
        cookie_str = "; ".join([f"{name}={value}" for name, value in self.__cookies.items()])
        reg_headers = {
            "Content-Type": "application/x-www-form-urlencoded",  # It's weired, but it works
            "Cookie": cookie_str,
        }

        logger.info(f"Subscribing to {reg_url}...")
        logger.info(f"Subscription Payload (as string): {reg_payload_str}")

        async with session.post(reg_url, data=reg_payload_str.encode("utf-8"), headers=reg_headers) as response:
            response_text = await response.text()
            logger.info(f"Subscription Status: {response.status}")
            logger.info(f"Subscription Response: {response_text}")
            if response.status == 200 and '"result":"success"' in response_text.lower():
                logger.info("Subscription successful.")
                return True
            else:
                logger.error(f"Subscription {products} - {types} failed: {response_text}")
                return False

    async def close(self):
        """
        Close the websocket connection.
        """
        logger.info("Preparing to close WebSocket connection...")
        if self.__ws_listener_task:
            self.__ws_listener_task.cancel()
            try:
                await self.__ws_listener_task
            except asyncio.CancelledError:
                logger.info("WebSocket listener task cancelled.")
        if self.__ws_connection:
            logger.info("Closing WebSocket connection...")
            await self.__ws_connection.close()
            self.__ws_connection = None
            logger.info("WebSocket connection closed.")
        if self.__http_session and not self.__http_session.closed:
            logger.info("Closing aiohttp.ClientSession...")
            await self.__http_session.close()
            logger.info("aiohttp.ClientSession closed.")
        logger.info("Client closed.")

    async def __get_sockjs_info(self):
        """
        Get sockjs info, prepare for websocket connection
        """
        session = await self.__ensure_session()

        cookie_str = "; ".join([f"{name}={value}" for name, value in self.__cookies.items()])
        timestamp = int(time.time() * 1000)
        info_url = f"{self.base_url}/wa/ws/info?t={timestamp}"
        info_headers = {"Cookie": cookie_str}

        logger.info(f"Fetching SockJS info from {info_url}...")

        async with session.get(info_url, headers=info_headers) as response:
            response_text = await response.text()
            logger.debug(f"SockJS Info Response: {response_text}")
            if response.status == 200:
                try:
                    info_data = json.loads(response_text)
                    logger.info(f"SockJS Info 獲取成功: {info_data}")
                    for key, value in response.cookies.items():  # 有些 API 可能在這裡也更新cookie
                        self.__cookies[key] = value
                    return info_data
                except json.JSONDecodeError as e:
                    logger.error(f"SockJS Info is not valid JSON: {response_text}")
                    raise e
            else:
                logger.error(f"Fetch SockJS info Failed, HTTP Status: {response.status}")
                raise Exception(f"Fetch SockJS info Failed, HTTP Status: {response.status}")

    async def __listen_websocket(self):
        """
        Listen to websocket messages.
        """
        if not self.__ws_connection:
            logger.error("WebSocket connection is not established. Cannot listen to messages.")
            return

        logger.info("Start listening to WebSocket messages...")
        try:
            async for message in self.__ws_connection:
                logger.debug(f"Received WebSocket message: {message}")
                parsed_data = None
                if message == "o":
                    logger.info("SockJS connection established.")
                    continue
                elif message == "h":
                    logger.debug("Heartbeat received.")
                    continue
                elif isinstance(message, str) and message.startswith("a["):
                    try:
                        # Array frame format of SockJS is 'a["json_string_payload"]'
                        payload_wrapper_str = message[1:]  # Remove the leading 'a'
                        payload_list = json.loads(payload_wrapper_str)

                        if payload_list and isinstance(payload_list, list):
                            for item_str in payload_list:
                                try:
                                    actual_data = json.loads(item_str)
                                    parsed_data = {"type": "data", "payload": actual_data}
                                    logger.info(f"Parse SockJS array frame successful, SockJS data: {actual_data}")
                                except json.JSONDecodeError:
                                    logger.debug(
                                        "Failed to parse item as direct JSON, attempting zlib/base64. "
                                        + f"Item: {item_str[:60]}..."
                                    )
                                    try:
                                        # Second try: base64 -> zlib -> json
                                        decoded_base64 = base64.b64decode(item_str)
                                        try:
                                            decompressed_data_bytes = zlib.decompress(decoded_base64)
                                        except zlib.error as zde:
                                            # if zlib.decompress failed, it may be raw deflate
                                            logger.debug(
                                                f"Standard zlib.decompress failed ({zde}), trying raw deflate for item."
                                            )
                                            decompress_obj = zlib.decompressobj(-zlib.MAX_WBITS)
                                            decompressed_data_bytes = decompress_obj.decompress(decoded_base64)
                                            decompressed_data_bytes += decompress_obj.flush()

                                        decompressed_json_str = decompressed_data_bytes.decode("utf-8")
                                        actual_data = json.loads(decompressed_json_str)

                                        logger.info(
                                            f"Successfully parsed item as zlib/base64 decoded JSON: {actual_data}"
                                        )
                                        if "payload" in actual_data and "hb" in actual_data["payload"]:
                                            logger.debug("Heartbeat received.")
                                            continue

                                    except Exception as e_zlib:
                                        # Second try failed
                                        logger.warning(
                                            f"Failed to parse item using zlib/base64. Item: {item_str[:60]}. "
                                            + f"Error: {e_zlib}"
                                        )
                                        parsed_data = {"type": "raw_sockjs_array_item", "payload": item_str}
                        else:
                            parsed_data = {"type": "unknown_sockjs_array", "payload": payload_list}

                    except json.JSONDecodeError:
                        logger.warning(f"Parse SockJS array frame (outer layer) failed: {message}")
                        parsed_data = {"type": "raw_message", "payload": message}  # Cannot parse, pass raw message
                else:
                    # Not SockJS array frame, may be normal message or error
                    logger.info(f"Received unknown type or normal WebSocket message: {message}")
                    parsed_data = {"type": "unknown", "payload": message}

                # Call all registered callbacks
                if parsed_data:
                    for callback in self.__message_callbacks:
                        asyncio.create_task(callback(parsed_data))

        except websockets.exceptions.ConnectionClosed as e:
            logger.warning(f"WebSocket connection closed, code: {e.code}, reason: {e.reason}")
        except Exception as e:
            logger.error(f"Error in WebSocket listener: {e}", exe_info=True)  # type: ignore
        finally:
            logger.info("WebSocket listener stopped.")
            self.__ws_connection = None  # Connection is closed, set to None
